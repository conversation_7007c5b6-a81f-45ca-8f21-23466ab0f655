# BERT剪枝项目 - Modal平台适配说明

## 🎯 项目概述

本项目成功将原始的Deep Compression剪枝算法适配到BERT模型，并专门针对Modal云平台进行了优化配置。

## 📁 核心文件说明

### 主要脚本

1. **`bert_pruning.py`** - 核心剪枝算法实现
   - 适配Modal平台的文件保存路径
   - 集成日志记录到文件
   - 自动保存详细结果摘要

2. **`run_bert_pruning_modal.py`** - Modal平台专用运行脚本
   - 提供友好的用户界面
   - 支持多种运行模式
   - 自动检查保存文件

### 配置文件

3. **`requirements_bert.txt`** - 依赖包列表
4. **`BERT_PRUNING_README.md`** - 详细使用说明

### 测试文件

5. **`test_bert_pruning.py`** - 功能测试脚本
6. **`example_usage.py`** - 使用示例

## 🔧 Modal平台特殊配置

### 文件保存路径配置

```python
SAVE_DIRS = {
    'model': '/download/model',      # 模型文件
    'image': '/download/image',      # 图像文件（备用）
    'other': '/download/other'       # 日志和摘要
}
```

### 关键特性

1. **自动目录创建** - 启动时自动创建所需目录
2. **双重日志记录** - 同时输出到控制台和文件
3. **详细结果摘要** - 自动生成包含所有重要信息的摘要文件
4. **完整文件检查** - 运行结束后验证所有文件是否正确保存

## 🚀 快速使用指南

### 1. 快速测试（推荐首次使用）

```bash
python run_bert_pruning_modal.py --quick_test
```

- 使用1000个样本
- 1轮微调训练
- 约5-10分钟完成

### 2. 标准剪枝（推荐生产使用）

```bash
python run_bert_pruning_modal.py --std_pruning --sensitivity 0.25
```

- 使用全部数据集
- 标准差剪枝方法
- 3轮微调训练

### 3. 自定义配置

```bash
python run_bert_pruning_modal.py --custom --sensitivity 0.3 --epochs 5 --batch_size 16
```

## 📊 输出文件详解

### 模型文件 (`/download/model/`)

- `original_model.pt` - 原始BERT模型权重
- `pruned_model.pt` - 剪枝后模型权重
- `final_model.pt` - 微调后最终模型权重

### 日志和报告 (`/download/other/`)

- `bert_pruning.log` - 完整运行日志
- `results_summary.txt` - 结构化结果摘要

### 结果摘要示例内容

```
BERT模型剪枝结果摘要
==================================================

实验配置:
- 剪枝方法: std
- 敏感度: 0.25
- 微调轮数: 3
- 学习率: 2e-05
- 批次大小: 16

准确率结果:
- 原始模型准确率: 0.9342
- 剪枝后准确率: 0.8956
- 微调后准确率: 0.9298
- 准确率下降: 0.0044 (0.47%)

压缩统计:
- 总参数数: 109,483,780
- 剪枝参数数: 71,038,567
- 剪枝比例: 64.91%
- 压缩比: 2.85x

层级剪枝详情（前20层）:
[详细的每层剪枝统计]
```

## ⚙️ 技术实现细节

### 核心改进

1. **路径配置系统**

   ```python
   def get_save_path(filename, file_type='other'):
       """获取文件的完整保存路径"""
       if file_type not in SAVE_DIRS:
           file_type = 'other'
       return os.path.join(SAVE_DIRS[file_type], filename)
   ```

2. **增强日志系统**
   - 同时写入控制台和文件
   - UTF-8编码支持
   - 时间戳格式化

3. **结果摘要生成**
   - 自动保存实验配置
   - 详细的层级统计
   - 性能指标汇总

### 剪枝算法特点

- **两种剪枝方法**: 标准差剪枝和百分位数剪枝
- **智能掩码管理**: 自动处理所有线性层
- **梯度管理**: 确保剪枝位置梯度为0
- **完整训练流程**: 原始评估→剪枝→微调→最终评估

## 🎯 预期结果

### 典型性能表现

- **压缩比**: 2-4倍
- **剪枝比例**: 50-70%
- **准确率下降**: < 1%
- **运行时间**: 30-60分钟（全数据集）

### 不同敏感度参数效果

| 敏感度 | 剪枝比例 | 压缩比 | 准确率影响 |
|--------|----------|--------|------------|
| 0.15   | ~40%     | ~1.7x  | 最小       |
| 0.25   | ~65%     | ~2.9x  | 轻微       |
| 0.35   | ~75%     | ~4.0x  | 中等       |
| 0.45   | ~85%     | ~6.7x  | 较大       |

## 🔍 故障排除

### 常见问题

1. **模型下载失败**
   - 检查网络连接
   - 确认Hugging Face访问正常

2. **内存不足**
   - 减小batch_size (如--batch_size 8)
   - 使用--max_samples限制数据量

3. **CUDA错误**
   - 添加--no_cuda使用CPU模式

### 验证运行结果

```bash
# 检查关键文件是否存在
ls -la /download/model/
ls -la /download/other/

# 查看结果摘要
cat /download/other/results_summary.txt
```

## 📈 后续扩展建议

1. **支持更多BERT变体** - 只需修改model_name参数
2. **添加新的剪枝策略** - 在BERTPruningModule中扩展
3. **集成量化和Huffman编码** - 复用原项目的其他压缩算法
4. **批量实验支持** - 自动测试多种参数组合

---

**本适配版本专为Modal云平台优化，确保所有重要数据都能正确保存到指定的下载目录中。**
