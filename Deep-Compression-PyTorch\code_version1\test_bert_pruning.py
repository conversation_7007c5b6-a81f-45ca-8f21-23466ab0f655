#!/usr/bin/env python3
"""
BERT剪枝测试脚本
用于验证主要功能是否正常工作

这个脚本会进行快速测试，确保：
1. 模型能正常加载
2. 剪枝算法能正常执行
3. 基本的训练流程能运行
"""

import torch
import torch.nn as nn
from transformers import AutoTokenizer, AutoModelForSequenceClassification
from bert_pruning import BERTPruningModule, print_model_info
import logging

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_model_loading():
    """测试模型加载"""
    logger.info("=== 测试模型加载 ===")
    try:
        tokenizer = AutoTokenizer.from_pretrained("textattack/bert-base-uncased-ag-news")
        model = AutoModelForSequenceClassification.from_pretrained("textattack/bert-base-uncased-ag-news")
        logger.info("✅ 模型加载成功")
        return model, tokenizer
    except Exception as e:
        logger.error(f"❌ 模型加载失败: {e}")
        return None, None

def test_data_loader_fix(tokenizer):
    """测试修复后的数据加载器"""
    logger.info("\n=== 测试数据加载器修复 ===")
    try:
        from bert_pruning import create_data_loader
        
        # 创建小数据集进行测试
        train_loader, val_loader = create_data_loader(tokenizer, batch_size=4, max_samples=100)
        logger.info(f"✅ 数据加载器创建成功")
        
        # 测试几个批次
        for i, batch in enumerate(val_loader):
            if i >= 3:  # 只测试前3个批次
                break
            logger.info(f"批次 {i+1}: input_ids={batch['input_ids'].shape}, "
                       f"attention_mask={batch['attention_mask'].shape}, "
                       f"labels={batch['label'].shape}")
        
        logger.info("✅ 数据加载器测试通过")
        return True
    except Exception as e:
        logger.error(f"❌ 数据加载器测试失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def test_pruning_module(model):
    """测试剪枝模块"""
    logger.info("\n=== 测试剪枝模块 ===")
    try:
        # 创建剪枝模块
        pruning_module = BERTPruningModule(model)
        logger.info(f"✅ 剪枝模块创建成功，找到 {len(pruning_module.masks)} 个可剪枝层")
        
        # 测试标准差剪枝
        logger.info("测试标准差剪枝...")
        pruning_module.prune_by_std(sensitivity=0.5)  # 使用较高的敏感度进行测试
        
        # 获取统计信息
        stats = pruning_module.get_pruning_stats()
        logger.info(f"✅ 剪枝完成: {stats['pruned_params']:,}/{stats['total_params']:,} "
                   f"({100 * stats['pruned_params'] / stats['total_params']:.1f}%) 参数被剪枝")
        logger.info(f"压缩比: {stats['compression_ratio']:.2f}x")
        
        return pruning_module, stats
    except Exception as e:
        logger.error(f"❌ 剪枝测试失败: {e}")
        return None, None

def test_model_inference(model, tokenizer):
    """测试模型推理"""
    logger.info("\n=== 测试模型推理 ===")
    try:
        # 准备测试数据
        test_texts = [
            "This is a great movie with excellent acting.",
            "The stock market is performing well today.",
            "Scientists discovered a new species in the Amazon.",
            "The latest smartphone has amazing features."
        ]
        
        model.eval()
        with torch.no_grad():
            for i, text in enumerate(test_texts):
                # Tokenize
                inputs = tokenizer(text, return_tensors="pt", truncation=True, padding=True, max_length=512)
                
                # Forward pass
                outputs = model(**inputs)
                logits = outputs.logits
                predicted_class = torch.argmax(logits, dim=-1).item()
                confidence = torch.softmax(logits, dim=-1).max().item()
                
                logger.info(f"文本 {i+1}: 预测类别={predicted_class}, 置信度={confidence:.3f}")
        
        logger.info("✅ 模型推理测试成功")
        return True
    except Exception as e:
        logger.error(f"❌ 模型推理测试失败: {e}")
        return False

def test_mask_application(pruning_module):
    """测试掩码应用"""
    logger.info("\n=== 测试掩码应用 ===")
    try:
        zero_weights_count = 0
        total_weights_count = 0
        
        for name, module in pruning_module.model.named_modules():
            if isinstance(module, nn.Linear) and name in pruning_module.masks:
                weight = module.weight.data
                mask = pruning_module.masks[name]
                
                # 检查掩码是否正确应用
                masked_weight = weight * mask
                zero_positions = (mask == 0)
                
                # 验证被掩码的位置确实为0
                assert torch.all(masked_weight[zero_positions] == 0), f"层 {name} 的掩码应用不正确"
                
                zero_weights_count += zero_positions.sum().item()
                total_weights_count += weight.numel()
        
        logger.info(f"✅ 掩码应用正确: {zero_weights_count}/{total_weights_count} 权重被置零")
        return True
    except Exception as e:
        logger.error(f"❌ 掩码应用测试失败: {e}")
        return False

def test_percentile_pruning(model):
    """测试百分位数剪枝"""
    logger.info("\n=== 测试百分位数剪枝 ===")
    try:
        pruning_module = BERTPruningModule(model)
        pruning_module.prune_by_percentile(percentile=20.0)  # 剪枝最小的20%权重
        
        stats = pruning_module.get_pruning_stats()
        logger.info(f"✅ 百分位数剪枝完成: {stats['pruned_params']:,}/{stats['total_params']:,} "
                   f"({100 * stats['pruned_params'] / stats['total_params']:.1f}%) 参数被剪枝")
        
        return True
    except Exception as e:
        logger.error(f"❌ 百分位数剪枝测试失败: {e}")
        return False

def main():
    """主测试函数"""
    logger.info("开始BERT剪枝功能测试...")
    logger.info("=" * 60)
    
    # 测试结果统计
    tests_passed = 0
    total_tests = 7
    
    # 1. 测试模型加载
    model, tokenizer = test_model_loading()
    if model is not None:
        tests_passed += 1
        
        # 打印模型信息
        print_model_info(model)
    else:
        logger.error("模型加载失败，跳过后续测试")
        return
    
    # 2. 测试数据加载器修复
    if test_data_loader_fix(tokenizer):
        tests_passed += 1
    
    # 3. 测试剪枝模块
    pruning_module, stats = test_pruning_module(model)
    if pruning_module is not None:
        tests_passed += 1
    
    # 4. 测试推理（剪枝后）
    if test_model_inference(model, tokenizer):
        tests_passed += 1
    
    # 5. 测试掩码应用
    if pruning_module is not None and test_mask_application(pruning_module):
        tests_passed += 1
    
    # 6. 重新加载模型进行百分位数剪枝测试
    model2, _ = test_model_loading()
    if model2 is not None and test_percentile_pruning(model2):
        tests_passed += 1
    
    # 7. 测试推理（百分位数剪枝后）
    if test_model_inference(model2, tokenizer):
        tests_passed += 1
    
    # 总结测试结果
    logger.info("\n" + "=" * 60)
    logger.info("测试结果总结:")
    logger.info(f"通过测试: {tests_passed}/{total_tests}")
    
    if tests_passed == total_tests:
        logger.info("🎉 所有测试通过！BERT剪枝功能工作正常")
        return True
    else:
        logger.warning(f"⚠️  有 {total_tests - tests_passed} 个测试失败")
        return False

if __name__ == '__main__':
    success = main()
    exit(0 if success else 1) 