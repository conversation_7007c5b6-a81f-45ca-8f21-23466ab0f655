# BERT剪枝优化指南

## 🔍 当前问题分析

### 问题1：除零错误 ✅ 已修复
- **原因**：统计计算时出现除零情况
- **解决**：添加了安全检查和默认值处理

### 问题2：压缩比过低
- **当前结果**：20.61%稀疏度，压缩比仅1.01x
- **原因分析**：
  1. CSR格式在低稀疏度时开销大
  2. 敏感度0.25过于保守
  3. BERT权重分布相对均匀

## 🎯 优化建议

### 1. 提高剪枝激进程度
```python
# 当前配置（保守）
success = run_compression(
    model_name='test_v4',
    steps='all',
    sensitivity=0.25,  # 太保守
    max_samples=10
)

# 建议配置（激进）
success = run_compression(
    model_name='test_v5_aggressive',
    steps='all',
    sensitivity=0.5,   # 更激进的剪枝
    max_samples=10
)
```

### 2. 使用百分位数剪枝
```python
# 百分位数剪枝通常更有效
success = run_compression(
    model_name='test_v5_percentile',
    steps='all',
    prune_method='percentile',
    percentile=10.0,  # 剪枝最小的10%权重
    max_samples=10
)
```

### 3. 分层剪枝策略
不同层使用不同的剪枝强度：
- **Attention层**：相对保守（sensitivity=0.3）
- **FFN层**：更激进（sensitivity=0.6）
- **Classifier层**：最保守（sensitivity=0.2）

## 📊 预期压缩效果

### 稀疏度与压缩比关系
- **20%稀疏度**：压缩比 ~1.0-1.2x（CSR开销大）
- **50%稀疏度**：压缩比 ~1.5-2.0x（开始有效）
- **70%稀疏度**：压缩比 ~2.5-3.5x（显著压缩）
- **90%稀疏度**：压缩比 ~5-10x（极致压缩）

### CSR格式效率阈值
- **稀疏度 < 30%**：CSR格式效率低
- **稀疏度 30-70%**：CSR格式开始有效
- **稀疏度 > 70%**：CSR格式非常有效

## 🚀 推荐的测试配置

### 配置1：中等激进
```python
success = run_compression(
    model_name='test_medium_aggressive',
    steps='all',
    prune_method='std',
    sensitivity=0.4,      # 中等激进
    epochs=2,
    max_samples=50,       # 稍多样本验证效果
    num_bits=8
)
```

### 配置2：高度激进
```python
success = run_compression(
    model_name='test_high_aggressive',
    steps='all',
    prune_method='percentile',
    percentile=15.0,      # 剪枝15%最小权重
    epochs=3,             # 更多epoch恢复精度
    max_samples=50,
    num_bits=8
)
```

### 配置3：极致压缩
```python
success = run_compression(
    model_name='test_extreme_compression',
    steps='all',
    prune_method='std',
    sensitivity=0.8,      # 极致剪枝
    epochs=5,             # 更多epoch恢复精度
    max_samples=100,      # 更多样本
    num_bits=4            # 更低位宽
)
```

## ⚠️ 注意事项

### 1. 精度与压缩的权衡
- 更激进的剪枝会降低模型精度
- 需要更多的微调epoch来恢复精度
- 建议逐步增加剪枝强度

### 2. 样本数量的影响
- `max_samples=10` 太少，可能导致过拟合
- 建议至少使用50-100个样本进行测试
- 生产环境建议使用完整数据集

### 3. 不同任务的敏感度
- **分类任务**：相对鲁棒，可以更激进剪枝
- **生成任务**：更敏感，需要保守剪枝
- **AG News分类**：可以尝试较激进的剪枝

## 🔧 快速修复建议

### 立即可尝试的配置
```python
# 在你的 run_modal.py 中修改为：
success = run_compression(
    model_name='test_v5_improved',
    steps='all',
    prune_method='percentile',
    percentile=12.0,      # 剪枝12%最小权重
    epochs=3,             # 增加微调轮数
    max_samples=50,       # 增加样本数
    sensitivity=0.25,     # 保持原值（percentile时不使用）
    num_bits=8
)
```

### 预期结果
- **稀疏度**：~40-50%
- **压缩比**：~1.8-2.5x
- **精度损失**：<5%（通过微调恢复）

## 📈 性能监控

关注以下指标：
1. **稀疏度**：目标 >40%
2. **压缩比**：目标 >2.0x
3. **精度保持**：目标 >95%原始精度
4. **训练时间**：应该显著减少

## 🎯 最终建议

1. **立即尝试**：使用百分位数剪枝，percentile=12.0
2. **逐步优化**：如果效果好，尝试percentile=15.0或20.0
3. **监控精度**：确保精度损失在可接受范围内
4. **完整测试**：满意后使用更多样本进行完整测试
