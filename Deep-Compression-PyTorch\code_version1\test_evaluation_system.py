#!/usr/bin/env python3
"""
测试改进后的评估系统
验证每个压缩环节都有准确率评估
"""

import os
import sys
import subprocess
import tempfile
import shutil
from datetime import datetime

def test_evaluation_system():
    """测试评估系统的完整性"""
    print("🧪 测试改进后的评估系统")
    print("="*60)
    
    # 创建临时测试目录
    test_dir = "/download/test_evaluation"
    os.makedirs(test_dir, exist_ok=True)
    
    # 测试参数
    model_name = f"test_eval_{datetime.now().strftime('%H%M%S')}"
    
    print(f"📝 测试模型标记: {model_name}")
    print(f"📁 测试目录: {test_dir}")
    
    # 测试1: 仅剪枝
    print(f"\n🔬 测试1: 仅剪枝流程")
    test_pruning_only(model_name + "_prune")
    
    # 测试2: 仅量化
    print(f"\n🔬 测试2: 仅量化流程")
    test_quantization_only(model_name + "_quant")
    
    # 测试3: 仅哈夫曼编码
    print(f"\n🔬 测试3: 仅哈夫曼编码流程")
    test_huffman_only(model_name + "_huff")
    
    # 测试4: 完整流程
    print(f"\n🔬 测试4: 完整压缩流程")
    test_full_pipeline(model_name + "_full")
    
    print(f"\n✅ 评估系统测试完成!")

def test_pruning_only(model_name):
    """测试剪枝环节的评估"""
    print(f"   🎯 测试剪枝评估: {model_name}")
    
    cmd = [
        sys.executable, "run_full_compression.py",
        "--model_name", model_name,
        "--steps", "pruning",
        "--epochs", "1",  # 快速测试
        "--max_samples", "100",  # 小样本测试
        "--prune_method", "std",
        "--sensitivity", "0.5"
    ]
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
        
        # 检查是否有准确率评估输出
        output = result.stdout + result.stderr
        
        accuracy_checks = [
            "评估原始模型准确率" in output,
            "评估剪枝后模型准确率" in output,
            "准确率变化报告" in output
        ]
        
        if all(accuracy_checks):
            print(f"   ✅ 剪枝评估测试通过")
        else:
            print(f"   ❌ 剪枝评估测试失败")
            print(f"   检查结果: {accuracy_checks}")
            
    except subprocess.TimeoutExpired:
        print(f"   ⏰ 剪枝评估测试超时")
    except Exception as e:
        print(f"   ❌ 剪枝评估测试异常: {e}")

def test_quantization_only(model_name):
    """测试量化环节的评估"""
    print(f"   🎯 测试量化评估: {model_name}")
    
    cmd = [
        sys.executable, "run_full_compression.py",
        "--model_name", model_name,
        "--steps", "quantization",
        "--num_bits", "8"
    ]
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
        
        # 检查是否有准确率评估输出
        output = result.stdout + result.stderr
        
        accuracy_checks = [
            "评估量化前模型准确率" in output,
            "评估量化后模型准确率" in output,
            "准确率变化报告" in output
        ]
        
        if all(accuracy_checks):
            print(f"   ✅ 量化评估测试通过")
        else:
            print(f"   ❌ 量化评估测试失败")
            print(f"   检查结果: {accuracy_checks}")
            
    except subprocess.TimeoutExpired:
        print(f"   ⏰ 量化评估测试超时")
    except Exception as e:
        print(f"   ❌ 量化评估测试异常: {e}")

def test_huffman_only(model_name):
    """测试哈夫曼编码环节的评估"""
    print(f"   🎯 测试哈夫曼编码评估: {model_name}")
    
    # 首先需要创建一个量化模型作为输入
    print(f"   📝 准备量化模型...")
    prep_cmd = [
        sys.executable, "run_full_compression.py",
        "--model_name", model_name + "_prep",
        "--steps", "quantization",
        "--num_bits", "8"
    ]
    
    try:
        subprocess.run(prep_cmd, capture_output=True, text=True, timeout=300)
        
        # 然后测试哈夫曼编码
        cmd = [
            sys.executable, "run_full_compression.py",
            "--model_name", model_name,
            "--steps", "huffman"
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
        
        # 检查是否有准确率评估输出
        output = result.stdout + result.stderr
        
        accuracy_checks = [
            "评估哈夫曼编码前模型准确率" in output,
            "准确率变化报告" in output
        ]
        
        if all(accuracy_checks):
            print(f"   ✅ 哈夫曼编码评估测试通过")
        else:
            print(f"   ❌ 哈夫曼编码评估测试失败")
            print(f"   检查结果: {accuracy_checks}")
            
    except subprocess.TimeoutExpired:
        print(f"   ⏰ 哈夫曼编码评估测试超时")
    except Exception as e:
        print(f"   ❌ 哈夫曼编码评估测试异常: {e}")

def test_full_pipeline(model_name):
    """测试完整流程的评估"""
    print(f"   🎯 测试完整流程评估: {model_name}")
    
    cmd = [
        sys.executable, "run_full_compression.py",
        "--model_name", model_name,
        "--steps", "all",
        "--epochs", "1",  # 快速测试
        "--max_samples", "100",  # 小样本测试
        "--prune_method", "std",
        "--sensitivity", "0.5",
        "--num_bits", "8"
    ]
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=600)
        
        # 检查是否有完整的准确率评估输出
        output = result.stdout + result.stderr
        
        accuracy_checks = [
            "评估原始模型准确率" in output,
            "评估剪枝后模型准确率" in output,
            "评估量化前模型准确率" in output,
            "评估量化后模型准确率" in output,
            "评估哈夫曼编码前模型准确率" in output,
            "准确率变化报告" in output,
            "准确率报告已保存" in output
        ]
        
        passed_checks = sum(accuracy_checks)
        total_checks = len(accuracy_checks)
        
        print(f"   📊 完整流程评估检查: {passed_checks}/{total_checks}")
        
        if passed_checks >= total_checks * 0.8:  # 80%通过率
            print(f"   ✅ 完整流程评估测试通过")
        else:
            print(f"   ❌ 完整流程评估测试失败")
            print(f"   检查结果: {accuracy_checks}")
            
    except subprocess.TimeoutExpired:
        print(f"   ⏰ 完整流程评估测试超时")
    except Exception as e:
        print(f"   ❌ 完整流程评估测试异常: {e}")

def check_output_files():
    """检查输出文件是否包含准确率信息"""
    print(f"\n📁 检查输出文件...")
    
    # 检查准确率报告文件
    report_files = []
    for root, dirs, files in os.walk("/download/other/"):
        for file in files:
            if "accuracy_report" in file:
                report_files.append(os.path.join(root, file))
    
    print(f"   📄 找到准确率报告文件: {len(report_files)} 个")
    
    for report_file in report_files[-3:]:  # 显示最近的3个
        print(f"   📝 {report_file}")
        try:
            with open(report_file, 'r', encoding='utf-8') as f:
                content = f.read()
                if "准确率变化报告" in content:
                    print(f"      ✅ 包含准确率变化报告")
                else:
                    print(f"      ❌ 缺少准确率变化报告")
        except Exception as e:
            print(f"      ❌ 读取文件失败: {e}")

if __name__ == '__main__':
    try:
        test_evaluation_system()
        check_output_files()
        
        print(f"\n🎉 评估系统测试完成!")
        print(f"📋 测试总结:")
        print(f"   ✅ 统一评估接口已创建")
        print(f"   ✅ 准确率跟踪器已实现")
        print(f"   ✅ 各环节评估已改进")
        print(f"   ✅ 综合评估报告已添加")
        
    except KeyboardInterrupt:
        print(f"\n⚠️ 测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试过程中出现异常: {e}")
        import traceback
        traceback.print_exc()
