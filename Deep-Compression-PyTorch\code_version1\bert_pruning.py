"""
BERT Model Pruning Script
基于Deep Compression论文的剪枝算法，专门针对BERT模型进行结构化剪枝

主要功能：
1. 加载预训练的BERT模型 (textattack/bert-base-uncased-ag-news)
2. 实现权重剪枝（按百分位数或标准差）
3. 支持剪枝后的微调训练
4. 提供详细的剪枝统计信息

使用方法：
python bert_pruning.py --epochs 3 --sensitivity 0.25 --prune_method std
"""

import argparse
import os
import warnings
from typing import Dict, Any, List, Tuple, Optional
import struct
import pickle

import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
from transformers import AutoTokenizer, AutoModelForSequenceClassification
from transformers import TrainingArguments, Trainer
from datasets import load_dataset, Dataset
from tqdm import tqdm
import logging

# 忽略一些警告
warnings.filterwarnings("ignore", category=UserWarning)

# 文件保存目录配置（适配Modal平台）
SAVE_DIRS = {
    'model': '/download/model',
    'image': '/download/image', 
    'other': '/download/other'
}

def setup_save_directories():
    """创建保存文件所需的目录结构"""
    for dir_type, dir_path in SAVE_DIRS.items():
        os.makedirs(dir_path, exist_ok=True)
        print(f"✓ {dir_type.capitalize()} directory ready: {dir_path}")

def get_save_path(filename, file_type='other'):
    """获取文件的完整保存路径"""
    if file_type not in SAVE_DIRS:
        file_type = 'other'
    return os.path.join(SAVE_DIRS[file_type], filename)

# 创建保存目录
setup_save_directories()

# 设置日志（适配Modal平台）
def setup_logging(model_name: str = 'bert_base'):
    """设置日志配置，同时输出到控制台和文件"""
    log_file = get_save_path(f'{model_name}_bert_pruning.log', 'other')

    # 创建日志格式
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')

    # 设置根日志器
    logger = logging.getLogger(__name__)
    logger.setLevel(logging.INFO)

    # 清除已有的处理器
    for handler in logger.handlers[:]:
        logger.removeHandler(handler)

    # 控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(formatter)

    # 文件处理器
    file_handler = logging.FileHandler(log_file, mode='w', encoding='utf-8')
    file_handler.setLevel(logging.INFO)
    file_handler.setFormatter(formatter)

    # 添加处理器
    logger.addHandler(console_handler)
    logger.addHandler(file_handler)

    logger.info(f"日志文件保存到: {log_file}")
    return logger

# 初始化默认logger，在main函数中会重新设置
logger = setup_logging()


class CSRSparseMatrix:
    """
    压缩稀疏行(CSR)格式的稀疏矩阵实现
    基于Deep Compression论文的稀疏矩阵存储格式
    """

    def __init__(self, values: np.ndarray, row_ptr: np.ndarray, col_indices: np.ndarray,
                 shape: Tuple[int, int], layer_type: str = 'fc'):
        """
        初始化CSR稀疏矩阵

        Args:
            values: 非零元素值数组
            row_ptr: 行指针数组，长度为 n_rows + 1
            col_indices: 列索引数组，长度等于非零元素个数
            shape: 原始矩阵形状 (rows, cols)
            layer_type: 层类型，'conv' 或 'fc'，用于确定索引差编码位数
        """
        self.values = values.astype(np.float32)
        self.row_ptr = row_ptr.astype(np.int32)
        self.col_indices = col_indices.astype(np.int32)
        self.shape = shape
        self.layer_type = layer_type
        self.compressed_indices = None
        self._compress_indices()

    def _compress_indices(self):
        """
        压缩列索引，使用索引差而非绝对位置
        conv层使用8位，fc层使用5位编码
        """
        if len(self.col_indices) == 0:
            self.compressed_indices = np.array([], dtype=np.uint8)
            return

        # 确定编码位数和最大差值
        if self.layer_type == 'conv':
            max_bits = 8
            max_diff = (1 << max_bits) - 1  # 255
        else:  # fc layer
            max_bits = 5
            max_diff = (1 << max_bits) - 1  # 31

        compressed = []

        for row_start, row_end in zip(self.row_ptr[:-1], self.row_ptr[1:]):
            if row_start >= row_end:
                continue

            row_indices = self.col_indices[row_start:row_end]
            if len(row_indices) == 0:
                continue

            # 计算索引差
            prev_idx = 0
            for idx in row_indices:
                diff = idx - prev_idx

                # 如果差值超过最大值，使用零填充
                while diff > max_diff:
                    compressed.append(max_diff)  # 添加最大值作为填充
                    diff -= max_diff
                    prev_idx += max_diff

                compressed.append(diff)
                prev_idx = idx

        self.compressed_indices = np.array(compressed, dtype=np.uint8)

    def to_dense(self) -> np.ndarray:
        """转换为稠密矩阵"""
        dense = np.zeros(self.shape, dtype=np.float32)

        for i in range(len(self.row_ptr) - 1):
            start = self.row_ptr[i]
            end = self.row_ptr[i + 1]

            for j in range(start, end):
                col = self.col_indices[j]
                dense[i, col] = self.values[j]

        return dense

    def get_compression_stats(self) -> Dict[str, Any]:
        """获取压缩统计信息"""
        original_size = self.shape[0] * self.shape[1] * 4  # float32

        # CSR存储大小：values + row_ptr + col_indices
        csr_size = (len(self.values) * 4 +  # values (float32)
                   len(self.row_ptr) * 4 +  # row_ptr (int32)
                   len(self.col_indices) * 4)  # col_indices (int32)

        # 压缩索引大小
        compressed_size = (len(self.values) * 4 +  # values (float32)
                          len(self.row_ptr) * 4 +  # row_ptr (int32)
                          len(self.compressed_indices) * 1)  # compressed indices (uint8)

        return {
            'original_size': original_size,
            'csr_size': csr_size,
            'compressed_size': compressed_size,
            'sparsity': 1.0 - len(self.values) / (self.shape[0] * self.shape[1]),
            'compression_ratio': original_size / compressed_size,
            'non_zero_elements': len(self.values)
        }


def dense_to_csr(matrix: np.ndarray, layer_type: str = 'fc') -> CSRSparseMatrix:
    """
    将稠密矩阵转换为CSR格式

    Args:
        matrix: 稠密矩阵
        layer_type: 层类型，'conv' 或 'fc'

    Returns:
        CSRSparseMatrix对象
    """
    if matrix.ndim != 2:
        raise ValueError("输入必须是2D矩阵")

    rows, cols = matrix.shape

    # 找到非零元素
    nonzero_rows, nonzero_cols = np.nonzero(matrix)
    values = matrix[nonzero_rows, nonzero_cols]

    # 构建行指针数组
    row_ptr = np.zeros(rows + 1, dtype=np.int32)
    for row in nonzero_rows:
        row_ptr[row + 1] += 1

    # 累积求和得到真正的行指针
    np.cumsum(row_ptr, out=row_ptr)

    return CSRSparseMatrix(values, row_ptr, nonzero_cols, (rows, cols), layer_type)


class BERTPruningModule:
    """
    BERT模型剪枝的主要类

    优化版本：减少不必要的开销，提高剪枝和微调速度
    支持：
    - 按百分位数剪枝
    - 按标准差剪枝
    - 延迟CSR转换（仅在保存时进行）
    - 高效的稀疏性约束
    """

    def __init__(self, model: nn.Module):
        """
        初始化剪枝模块

        Args:
            model: BERT模型实例
        """
        self.model = model
        self.sparse_masks = {}  # 存储稀疏掩码（用于快速训练）
        self.sparse_weights = {}  # 存储CSR稀疏矩阵（延迟创建）
        self.original_shapes = {}  # 存储原始权重形状
        self.layer_types = {}  # 存储层类型信息
        self._analyze_layers()

    def _analyze_layers(self):
        """
        分析模型中的线性层，确定层类型
        """
        for name, module in self.model.named_modules():
            if isinstance(module, nn.Linear) and 'bias' not in name:
                # 根据层名称判断类型
                if 'conv' in name.lower() or 'convolution' in name.lower():
                    layer_type = 'conv'
                else:
                    layer_type = 'fc'  # 大部分BERT层都是全连接层

                self.layer_types[name] = layer_type
                self.original_shapes[name] = module.weight.shape
                logger.info(f"发现可剪枝层: {name}, 形状: {module.weight.shape}, 类型: {layer_type}")

    def _get_layer_type(self, layer_name: str) -> str:
        """获取层类型"""
        return self.layer_types.get(layer_name, 'fc')
    
    def prune_by_percentile(self, percentile: float = 5.0, exclude_layers: List[str] = None):
        """
        按百分位数进行结构化剪枝

        Args:
            percentile: 剪枝百分位数（0-100），例如5.0表示剪枝最小的5%权重
            exclude_layers: 要排除的层名称列表
        """
        if exclude_layers is None:
            exclude_layers = []

        # 收集所有可剪枝的权重
        all_weights = []
        for name, module in self.model.named_modules():
            if (isinstance(module, nn.Linear) and
                name not in exclude_layers and
                'bias' not in name):

                weight = module.weight.data.cpu().numpy().flatten()
                # 只考虑非零权重（避免重复剪枝已经为0的权重）
                nonzero_weights = weight[weight != 0]
                if len(nonzero_weights) > 0:
                    all_weights.append(nonzero_weights)

        if not all_weights:
            logger.warning("没有找到可剪枝的权重")
            return

        # 计算全局阈值
        all_weights_concat = np.concatenate(all_weights)
        threshold = np.percentile(np.abs(all_weights_concat), percentile)

        logger.info(f'使用阈值进行剪枝: {threshold:.6f}')

        # 应用结构化剪枝
        self._apply_structured_pruning(threshold, exclude_layers)

    def _apply_structured_pruning(self, threshold: float, exclude_layers: List[str]):
        """
        应用结构化剪枝，使用mask进行快速剪枝
        CSR转换延迟到保存时进行，提高训练效率

        Args:
            threshold: 剪枝阈值
            exclude_layers: 要排除的层名称列表
        """
        total_params = 0
        pruned_params = 0

        for name, module in self.model.named_modules():
            if (isinstance(module, nn.Linear) and
                name not in exclude_layers and
                'bias' not in name):

                # 获取权重矩阵
                weight = module.weight.data

                # 创建稀疏掩码
                mask = (torch.abs(weight) >= threshold).float()

                # 应用掩码到权重
                module.weight.data *= mask

                # 存储掩码（用于训练时的稀疏性约束）
                self.sparse_masks[name] = mask

                # 统计信息
                total_params += weight.numel()
                pruned_params += (mask == 0).sum().item()

                sparsity = (mask == 0).sum().item() / weight.numel()
                logger.info(f'层 {name}: 剪枝 {(mask == 0).sum().item()}/{weight.numel()} '
                           f'({100 * sparsity:.2f}%) 参数')

        # 总体统计
        logger.info(f'总体剪枝: {pruned_params}/{total_params} '
                   f'({100 * pruned_params / total_params:.2f}%) 参数')

        # 延迟创建CSR稀疏矩阵（仅在需要时）
        logger.info("✅ 剪枝完成，CSR转换将在保存时进行")
    
    def prune_by_std(self, sensitivity: float = 0.25, exclude_layers: List[str] = None):
        """
        按标准差进行结构化剪枝

        Args:
            sensitivity: 敏感度参数，阈值 = std * sensitivity
            exclude_layers: 要排除的层名称列表
        """
        if exclude_layers is None:
            exclude_layers = []

        total_params = 0
        pruned_params = 0
        total_original_size = 0
        total_compressed_size = 0

        for name, module in self.model.named_modules():
            if (isinstance(module, nn.Linear) and
                name not in exclude_layers and
                'bias' not in name):

                # 获取权重矩阵
                weight = module.weight.data.cpu().numpy()
                std = np.std(weight)
                threshold = std * sensitivity

                # 应用阈值剪枝
                pruned_weight = np.where(np.abs(weight) >= threshold, weight, 0)

                # 转换为CSR稀疏矩阵
                layer_type = self._get_layer_type(name)
                sparse_matrix = dense_to_csr(pruned_weight, layer_type)

                # 存储稀疏矩阵
                self.sparse_weights[name] = sparse_matrix

                # 获取压缩统计
                stats = sparse_matrix.get_compression_stats()

                # 统计信息
                total_params += weight.size
                pruned_params += weight.size - stats['non_zero_elements']
                total_original_size += stats['original_size']
                total_compressed_size += stats['compressed_size']

                logger.info(f'层 {name}: std={std:.6f}, threshold={threshold:.6f}, '
                           f'剪枝 {weight.size - stats["non_zero_elements"]}/{weight.size} '
                           f'({100 * stats["sparsity"]:.2f}%) 参数, '
                           f'压缩比: {stats["compression_ratio"]:.2f}x')

        # 总体统计
        overall_compression_ratio = total_original_size / total_compressed_size if total_compressed_size > 0 else 1.0
        logger.info(f'总体剪枝: {pruned_params}/{total_params} '
                   f'({100 * pruned_params / total_params:.2f}%) 参数')
        logger.info(f'存储压缩比: {overall_compression_ratio:.2f}x')
    
    def _create_sparse_weights_from_masks(self):
        """
        从稀疏掩码创建CSR稀疏矩阵（延迟转换）
        """
        logger.info("🔄 开始CSR稀疏矩阵转换...")

        for name, module in self.model.named_modules():
            if isinstance(module, nn.Linear) and name in self.sparse_masks:
                # 获取当前权重和掩码
                weight = module.weight.data.cpu().numpy()
                mask = self.sparse_masks[name].cpu().numpy()

                # 应用掩码得到稀疏权重
                sparse_weight = weight * mask

                # 转换为CSR格式
                layer_type = self._get_layer_type(name)
                sparse_matrix = dense_to_csr(sparse_weight, layer_type)

                # 存储稀疏矩阵
                self.sparse_weights[name] = sparse_matrix

                logger.info(f"✅ 层 {name} CSR转换完成")

        logger.info("✅ 所有层CSR转换完成")

    def get_pruning_stats(self) -> Dict[str, Any]:
        """
        获取剪枝统计信息

        Returns:
            包含剪枝统计的字典
        """
        # 如果还没有创建CSR稀疏矩阵，先创建
        if not self.sparse_weights and self.sparse_masks:
            self._create_sparse_weights_from_masks()

        stats = {
            'layer_stats': {},
            'total_params': 0,
            'pruned_params': 0,
            'total_original_size': 0,
            'total_compressed_size': 0,
            'compression_ratio': 1.0
        }

        if self.sparse_weights:
            # 使用CSR稀疏矩阵的统计
            for name, sparse_matrix in self.sparse_weights.items():
                matrix_stats = sparse_matrix.get_compression_stats()

                total_elements = sparse_matrix.shape[0] * sparse_matrix.shape[1]
                pruned_elements = total_elements - matrix_stats['non_zero_elements']

                stats['layer_stats'][name] = {
                    'total_params': total_elements,
                    'pruned_params': pruned_elements,
                    'pruning_ratio': matrix_stats['sparsity'],
                    'shape': list(sparse_matrix.shape),
                    'compression_ratio': matrix_stats['compression_ratio'],
                    'original_size': matrix_stats['original_size'],
                    'compressed_size': matrix_stats['compressed_size']
                }

                stats['total_params'] += total_elements
                stats['pruned_params'] += pruned_elements
                stats['total_original_size'] += matrix_stats['original_size']
                stats['total_compressed_size'] += matrix_stats['compressed_size']
        else:
            # 使用掩码的快速统计
            for name, mask in self.sparse_masks.items():
                if name in self.original_shapes:
                    shape = self.original_shapes[name]
                    total_elements = shape[0] * shape[1]
                    pruned_elements = (mask == 0).sum().item()
                    non_zero_elements = total_elements - pruned_elements

                    # 估算CSR压缩后的大小
                    original_size = total_elements * 4  # float32
                    # CSR格式：values + row_ptr + col_indices
                    estimated_compressed_size = (
                        non_zero_elements * 4 +  # values (float32)
                        (shape[0] + 1) * 4 +     # row_ptr (int32)
                        non_zero_elements * 1    # compressed indices (uint8)
                    )

                    # 计算压缩比
                    compression_ratio = original_size / estimated_compressed_size if estimated_compressed_size > 0 else 1.0

                    stats['layer_stats'][name] = {
                        'total_params': total_elements,
                        'pruned_params': pruned_elements,
                        'pruning_ratio': pruned_elements / total_elements,
                        'shape': list(shape),
                        'compression_ratio': compression_ratio,
                        'original_size': original_size,
                        'compressed_size': estimated_compressed_size
                    }

                    stats['total_params'] += total_elements
                    stats['pruned_params'] += pruned_elements
                    stats['total_original_size'] += original_size
                    stats['total_compressed_size'] += estimated_compressed_size

        if stats['total_compressed_size'] > 0:
            stats['compression_ratio'] = stats['total_original_size'] / stats['total_compressed_size']

        return stats

    def create_sparse_model(self) -> Dict[str, Any]:
        """
        创建包含稀疏权重的模型状态字典

        Returns:
            包含稀疏权重信息的字典
        """
        # 确保CSR稀疏矩阵已创建
        if not self.sparse_weights and self.sparse_masks:
            self._create_sparse_weights_from_masks()

        sparse_state = {
            'sparse_weights': {},
            'layer_types': self.layer_types,
            'original_shapes': self.original_shapes,
            'model_config': {
                'model_class': self.model.__class__.__name__,
                'num_labels': getattr(self.model.config, 'num_labels', 4)
            }
        }

        # 保存稀疏权重
        for name, sparse_matrix in self.sparse_weights.items():
            sparse_state['sparse_weights'][name] = {
                'values': sparse_matrix.values,
                'row_ptr': sparse_matrix.row_ptr,
                'col_indices': sparse_matrix.col_indices,
                'compressed_indices': sparse_matrix.compressed_indices,
                'shape': sparse_matrix.shape,
                'layer_type': sparse_matrix.layer_type
            }

        # 保存非剪枝的权重（如bias等）
        sparse_state['dense_weights'] = {}
        for name, param in self.model.named_parameters():
            if 'bias' in name:  # 保存所有bias参数
                sparse_state['dense_weights'][name] = param.data.cpu().numpy()

        return sparse_state


def create_data_loader(tokenizer, batch_size: int = 16, max_samples: int = None):
    """
    创建AG News数据集的数据加载器
    
    Args:
        tokenizer: BERT tokenizer
        batch_size: 批次大小
        max_samples: 最大样本数（用于快速测试）
        
    Returns:
        train_loader, val_loader
    """
    # 加载AG News数据集
    dataset = load_dataset("ag_news")
    
    def tokenize_function(examples):
        # 不在这里padding，而是在DataCollator中动态padding
        return tokenizer(examples["text"], truncation=True, max_length=512)
    
    # 如果指定了最大样本数，则截取
    if max_samples:
        train_dataset = dataset["train"].select(range(min(max_samples, len(dataset["train"]))))
        test_dataset = dataset["test"].select(range(min(max_samples//4, len(dataset["test"]))))
    else:
        train_dataset = dataset["train"]
        test_dataset = dataset["test"]
    
    # 应用tokenization
    train_dataset = train_dataset.map(tokenize_function, batched=True)
    test_dataset = test_dataset.map(tokenize_function, batched=True)
    
    # 设置format
    train_dataset.set_format(type="torch", columns=["input_ids", "attention_mask", "label"])
    test_dataset.set_format(type="torch", columns=["input_ids", "attention_mask", "label"])
    
    # 自定义collate函数，确保正确的padding
    def collate_fn(batch):
        """自定义的collate函数，处理变长序列"""
        # 提取各个字段
        input_ids = [item['input_ids'] for item in batch]
        attention_masks = [item['attention_mask'] for item in batch]
        labels = [item['label'] for item in batch]
        
        # Padding到批次中的最大长度
        max_len = max(len(ids) for ids in input_ids)
        
        # Pad input_ids和attention_masks
        padded_input_ids = []
        padded_attention_masks = []
        
        for ids, mask in zip(input_ids, attention_masks):
            # 计算需要padding的长度
            pad_len = max_len - len(ids)
            
            # Padding input_ids（使用tokenizer的pad_token_id）
            padded_ids = torch.cat([ids, torch.full((pad_len,), tokenizer.pad_token_id, dtype=ids.dtype)])
            padded_input_ids.append(padded_ids)
            
            # Padding attention_mask（使用0）
            padded_mask = torch.cat([mask, torch.zeros(pad_len, dtype=mask.dtype)])
            padded_attention_masks.append(padded_mask)
        
        # 堆叠成批次
        return {
            'input_ids': torch.stack(padded_input_ids),
            'attention_mask': torch.stack(padded_attention_masks),
            'label': torch.stack(labels)
        }
    
    # 创建数据加载器，使用自定义collate function
    train_loader = torch.utils.data.DataLoader(
        train_dataset, 
        batch_size=batch_size, 
        shuffle=True,
        collate_fn=collate_fn
    )
    val_loader = torch.utils.data.DataLoader(
        test_dataset, 
        batch_size=batch_size, 
        shuffle=False,
        collate_fn=collate_fn
    )
    
    return train_loader, val_loader


def evaluate_model(model, val_loader, device):
    """
    评估模型性能
    
    Args:
        model: 要评估的模型
        val_loader: 验证数据加载器
        device: 设备
        
    Returns:
        accuracy: 准确率
    """
    model.eval()
    total_correct = 0
    total_samples = 0
    total_loss = 0
    
    with torch.no_grad():
        for batch in tqdm(val_loader, desc="评估中"):
            input_ids = batch['input_ids'].to(device)
            attention_mask = batch['attention_mask'].to(device)
            labels = batch['label'].to(device)
            
            outputs = model(input_ids=input_ids, attention_mask=attention_mask, labels=labels)
            loss = outputs.loss
            logits = outputs.logits
            
            predictions = torch.argmax(logits, dim=-1)
            total_correct += (predictions == labels).sum().item()
            total_samples += labels.size(0)
            total_loss += loss.item()
    
    accuracy = total_correct / total_samples
    avg_loss = total_loss / len(val_loader)
    
    logger.info(f'验证准确率: {accuracy:.4f} ({total_correct}/{total_samples})')
    logger.info(f'验证损失: {avg_loss:.4f}')
    
    return accuracy


def fine_tune_model(model, train_loader, val_loader, device, pruning_module, epochs: int = 3, learning_rate: float = 2e-5):
    """
    对剪枝后的模型进行微调
    优化版本：使用高效的稀疏性约束，减少训练开销

    Args:
        model: 要微调的模型
        train_loader: 训练数据加载器
        val_loader: 验证数据加载器
        device: 设备
        pruning_module: 剪枝模块实例，用于管理稀疏权重
        epochs: 训练轮数
        learning_rate: 学习率
    """
    model.train()
    optimizer = optim.AdamW(model.parameters(), lr=learning_rate, weight_decay=0.01)

    # 预先将稀疏掩码移动到GPU，避免重复传输
    gpu_masks = {}
    for name, mask in pruning_module.sparse_masks.items():
        gpu_masks[name] = mask.to(device)

    for epoch in range(epochs):
        total_loss = 0
        model.train()

        progress_bar = tqdm(train_loader, desc=f"训练 Epoch {epoch + 1}/{epochs}")

        for batch in progress_bar:
            optimizer.zero_grad()

            input_ids = batch['input_ids'].to(device)
            attention_mask = batch['attention_mask'].to(device)
            labels = batch['label'].to(device)

            outputs = model(input_ids=input_ids, attention_mask=attention_mask, labels=labels)
            loss = outputs.loss

            loss.backward()

            # 高效的稀疏性约束：直接使用GPU上的掩码
            for name, module in model.named_modules():
                if isinstance(module, nn.Linear) and name in gpu_masks:
                    if module.weight.grad is not None:
                        module.weight.grad.data *= gpu_masks[name]

            optimizer.step()

            # 训练后重新应用稀疏性约束到权重
            for name, module in model.named_modules():
                if isinstance(module, nn.Linear) and name in gpu_masks:
                    module.weight.data *= gpu_masks[name]

            total_loss += loss.item()
            progress_bar.set_postfix({'loss': f'{loss.item():.4f}'})

        avg_loss = total_loss / len(train_loader)
        logger.info(f'Epoch {epoch + 1}/{epochs}, 平均训练损失: {avg_loss:.4f}')

        # 每个epoch后评估
        accuracy = evaluate_model(model, val_loader, device)


def _load_sparse_weights_to_model(model, pruning_module, device):
    """
    将稀疏权重加载到模型中进行微调
    """
    for name, module in model.named_modules():
        if isinstance(module, nn.Linear) and name in pruning_module.sparse_weights:
            sparse_matrix = pruning_module.sparse_weights[name]
            dense_weight = torch.from_numpy(sparse_matrix.to_dense()).to(device)
            module.weight.data = dense_weight


def _apply_sparsity_constraint(model, pruning_module, device):
    """
    应用稀疏性约束，确保剪枝位置保持为0
    """
    for name, module in model.named_modules():
        if isinstance(module, nn.Linear) and name in pruning_module.sparse_weights:
            sparse_matrix = pruning_module.sparse_weights[name]
            dense_weight = sparse_matrix.to_dense()
            sparsity_mask = torch.from_numpy(dense_weight != 0).float().to(device)

            # 应用稀疏性约束到权重
            module.weight.data *= sparsity_mask

            # 应用稀疏性约束到梯度
            if module.weight.grad is not None:
                module.weight.grad.data *= sparsity_mask


def _update_sparse_weights_from_model(model, pruning_module):
    """
    从微调后的模型中更新稀疏掩码和权重
    优化版本：只更新掩码，延迟CSR转换
    """
    for name, module in model.named_modules():
        if isinstance(module, nn.Linear) and name in pruning_module.sparse_masks:
            # 获取微调后的权重
            updated_weight = module.weight.data

            # 更新稀疏掩码（保持稀疏结构）
            mask = pruning_module.sparse_masks[name].to(updated_weight.device)

            # 确保权重保持稀疏性
            module.weight.data *= mask

            # 更新存储的掩码
            pruning_module.sparse_masks[name] = mask.cpu()

            logger.info(f"已更新层 {name} 的稀疏掩码")

    # 清除旧的CSR稀疏矩阵，强制重新创建
    pruning_module.sparse_weights.clear()
    logger.info("✅ 稀疏权重更新完成，CSR矩阵将在保存时重新创建")


def print_model_info(model):
    """打印模型信息"""
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    
    logger.info(f"模型总参数数: {total_params:,}")
    logger.info(f"可训练参数数: {trainable_params:,}")
    
    logger.info("\n模型结构概览:")
    for name, module in model.named_modules():
        if isinstance(module, nn.Linear):
            logger.info(f"  {name}: {module.weight.shape}")

def save_sparse_model(pruning_module, model_name: str, stage: str = 'pruned') -> str:
    """
    保存稀疏模型到文件

    Args:
        pruning_module: 剪枝模块实例
        model_name: 模型名称标识
        stage: 阶段标识 ('pruned', 'final')

    Returns:
        保存的文件路径
    """
    sparse_state = pruning_module.create_sparse_model()

    # 根据model_name和stage生成文件名
    filename = f"{model_name}_{stage}_sparse.pkl"
    save_path = get_save_path(filename, 'model')

    # 保存稀疏模型
    with open(save_path, 'wb') as f:
        pickle.dump(sparse_state, f)

    logger.info(f"稀疏模型已保存到: {save_path}")

    # 计算并显示压缩统计（添加安全检查）
    stats = pruning_module.get_pruning_stats()

    # 安全的统计信息显示
    total_original_size = stats.get('total_original_size', 0)
    total_compressed_size = stats.get('total_compressed_size', 0)
    total_params = stats.get('total_params', 0)
    pruned_params = stats.get('pruned_params', 0)
    compression_ratio = stats.get('compression_ratio', 1.0)

    logger.info(f"模型压缩统计:")
    logger.info(f"  - 原始大小: {total_original_size / 1024 / 1024:.2f} MB")
    logger.info(f"  - 压缩后大小: {total_compressed_size / 1024 / 1024:.2f} MB")
    logger.info(f"  - 压缩比: {compression_ratio:.2f}x")

    # 安全的稀疏度计算
    if total_params > 0:
        sparsity = 100 * pruned_params / total_params
        logger.info(f"  - 稀疏度: {sparsity:.2f}%")
    else:
        logger.warning("  - 稀疏度: 无法计算（总参数数为0）")

    return save_path


def save_results_summary(original_accuracy, pruned_accuracy, final_accuracy, stats, args, model_name: str):
    """保存结果摘要到文件"""
    summary_file = get_save_path(f'{model_name}_results_summary.txt', 'other')

    with open(summary_file, 'w', encoding='utf-8') as f:
        f.write(f"BERT模型剪枝结果摘要 - {model_name}\n")
        f.write("=" * 50 + "\n\n")

        # 实验配置
        f.write("实验配置:\n")
        f.write(f"- 模型标识: {model_name}\n")
        f.write(f"- 剪枝方法: {args.prune_method}\n")
        if args.prune_method == 'std':
            f.write(f"- 敏感度: {args.sensitivity}\n")
        else:
            f.write(f"- 百分位数: {args.percentile}\n")
        f.write(f"- 微调轮数: {args.epochs}\n")
        f.write(f"- 学习率: {args.learning_rate}\n")
        f.write(f"- 批次大小: {args.batch_size}\n")
        f.write(f"- 最大样本数: {args.max_samples or '全部'}\n\n")

        # 准确率结果
        f.write("准确率结果:\n")
        f.write(f"- 原始模型准确率: {original_accuracy:.4f}\n")
        f.write(f"- 剪枝后准确率: {pruned_accuracy:.4f}\n")
        f.write(f"- 微调后准确率: {final_accuracy:.4f}\n")
        f.write(f"- 准确率下降: {(original_accuracy - final_accuracy):.4f} "
               f"({100 * (original_accuracy - final_accuracy) / original_accuracy:.2f}%)\n\n")

        # 压缩统计
        f.write("压缩统计:\n")
        f.write(f"- 总参数数: {stats['total_params']:,}\n")
        f.write(f"- 剪枝参数数: {stats['pruned_params']:,}\n")
        f.write(f"- 剪枝比例: {100 * stats['pruned_params'] / stats['total_params']:.2f}%\n")
        f.write(f"- 存储压缩比: {stats['compression_ratio']:.2f}x\n")
        f.write(f"- 原始大小: {stats['total_original_size'] / 1024 / 1024:.2f} MB\n")
        f.write(f"- 压缩后大小: {stats['total_compressed_size'] / 1024 / 1024:.2f} MB\n\n")

        # 模型文件路径
        f.write("保存的模型文件:\n")
        f.write(f"- 原始模型: {get_save_path(f'{model_name}_original.pt', 'model')}\n")
        f.write(f"- 剪枝后稀疏模型: {get_save_path(f'{model_name}_pruned_sparse.pkl', 'model')}\n")
        f.write(f"- 最终稀疏模型: {get_save_path(f'{model_name}_final_sparse.pkl', 'model')}\n\n")

        # 层级详细统计（前20层）
        f.write("层级剪枝详情（前20层）:\n")
        f.write(f"{'层名称':<40} {'剪枝比例':<10} {'压缩比':<10} {'剪枝参数':<12} {'总参数':<12}\n")
        f.write("-" * 90 + "\n")

        sorted_layers = sorted(stats['layer_stats'].items(),
                              key=lambda x: x[1]['pruning_ratio'],
                              reverse=True)

        for layer_name, layer_info in sorted_layers[:20]:
            f.write(f"{layer_name:<40} "
                   f"{100 * layer_info['pruning_ratio']:<10.2f}% "
                   f"{layer_info['compression_ratio']:<10.2f}x "
                   f"{layer_info['pruned_params']:<12,} "
                   f"{layer_info['total_params']:<12,}\n")

    logger.info(f"结果摘要已保存到: {summary_file}")
    return summary_file


def main():
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='BERT模型剪枝脚本')
    parser.add_argument('--model_name', type=str, default='bert_base_v1',
                        help='模型标识名称，用于文件命名 (默认: bert_base_v1)')
    parser.add_argument('--pretrained_model', type=str, default='textattack/bert-base-uncased-ag-news',
                        help='要加载的预训练BERT模型名称')
    parser.add_argument('--batch_size', type=int, default=16,
                        help='批次大小 (默认: 16)')
    parser.add_argument('--epochs', type=int, default=3,
                        help='微调的训练轮数 (默认: 3)')
    parser.add_argument('--learning_rate', type=float, default=2e-5,
                        help='学习率 (默认: 2e-5)')
    parser.add_argument('--prune_method', type=str, choices=['percentile', 'std'], default='std',
                        help='剪枝方法: percentile 或 std (默认: std)')
    parser.add_argument('--percentile', type=float, default=5.0,
                        help='百分位数剪枝的百分位数 (默认: 5.0)')
    parser.add_argument('--sensitivity', type=float, default=0.25,
                        help='标准差剪枝的敏感度参数 (默认: 0.25)')
    parser.add_argument('--max_samples', type=int, default=None,
                        help='最大样本数，用于快速测试 (默认: None，使用全部数据)')
    parser.add_argument('--no_cuda', action='store_true', default=False,
                        help='禁用CUDA')
    parser.add_argument('--save_path', type=str, default=None,
                        help='模型保存路径（已自动配置为Modal平台路径）')

    args = parser.parse_args()

    # 重新设置logger，使用model_name
    global logger
    logger = setup_logging(args.model_name)

    # 设置设备
    device = torch.device('cuda' if torch.cuda.is_available() and not args.no_cuda else 'cpu')
    logger.info(f'使用设备: {device}')
    logger.info(f'模型标识: {args.model_name}')
    
    # 加载模型和tokenizer
    logger.info(f'加载预训练模型: {args.pretrained_model}')
    logger.info(f'模型标识: {args.model_name}')
    tokenizer = AutoTokenizer.from_pretrained(args.pretrained_model)
    model = AutoModelForSequenceClassification.from_pretrained(args.pretrained_model)
    model.to(device)

    # 打印模型信息
    logger.info("=== 原始模型信息 ===")
    print_model_info(model)

    # 创建数据加载器
    logger.info("创建数据加载器...")
    try:
        train_loader, val_loader = create_data_loader(tokenizer, args.batch_size, args.max_samples)
        logger.info(f"数据加载器创建成功，训练集批次数: {len(train_loader)}, 验证集批次数: {len(val_loader)}")

        # 测试数据加载器
        logger.info("测试数据加载器...")
        test_batch = next(iter(val_loader))
        logger.info(f"测试批次形状: input_ids={test_batch['input_ids'].shape}, "
                   f"attention_mask={test_batch['attention_mask'].shape}, "
                   f"labels={test_batch['label'].shape}")
    except Exception as e:
        logger.error(f"数据加载器创建失败: {e}")
        raise

    # 评估原始模型
    logger.info("\n=== 评估原始模型 ===")
    original_accuracy = evaluate_model(model, val_loader, device)

    # 保存原始模型
    original_save_path = get_save_path(f'{args.model_name}_original.pt', 'model')
    torch.save(model.state_dict(), original_save_path)
    logger.info(f'原始模型已保存到: {original_save_path}')
    
    # 创建剪枝模块
    logger.info("\n=== 开始剪枝 ===")
    pruning_module = BERTPruningModule(model)

    # 执行剪枝
    if args.prune_method == 'percentile':
        logger.info(f'使用百分位数剪枝，百分位数: {args.percentile}')
        pruning_module.prune_by_percentile(args.percentile)
    else:
        logger.info(f'使用标准差剪枝，敏感度: {args.sensitivity}')
        pruning_module.prune_by_std(args.sensitivity)

    # 获取剪枝统计
    stats = pruning_module.get_pruning_stats()
    logger.info(f"\n=== 剪枝统计 ===")
    logger.info(f"总参数数: {stats['total_params']:,}")
    logger.info(f"剪枝参数数: {stats['pruned_params']:,}")
    logger.info(f"剪枝比例: {100 * stats['pruned_params'] / stats['total_params']:.2f}%")
    logger.info(f"存储压缩比: {stats['compression_ratio']:.2f}x")
    logger.info(f"原始大小: {stats['total_original_size'] / 1024 / 1024:.2f} MB")
    logger.info(f"压缩后大小: {stats['total_compressed_size'] / 1024 / 1024:.2f} MB")

    # 评估剪枝后的模型
    logger.info("\n=== 评估剪枝后模型 ===")
    pruned_accuracy = evaluate_model(model, val_loader, device)

    # 保存剪枝后的稀疏模型
    pruned_save_path = save_sparse_model(pruning_module, args.model_name, 'pruned')
    logger.info(f'剪枝后稀疏模型已保存到: {pruned_save_path}')
    
    # 微调剪枝后的模型
    logger.info(f"\n=== 开始微调 ({args.epochs} epochs) ===")
    fine_tune_model(model, train_loader, val_loader, device, pruning_module, args.epochs, args.learning_rate)

    # 更新稀疏权重（从微调后的模型中提取）
    _update_sparse_weights_from_model(model, pruning_module)

    # 评估微调后的模型
    logger.info("\n=== 评估微调后模型 ===")
    final_accuracy = evaluate_model(model, val_loader, device)

    # 保存最终稀疏模型
    final_save_path = save_sparse_model(pruning_module, args.model_name, 'final')
    logger.info(f'最终稀疏模型已保存到: {final_save_path}')

    # 为了兼容后续的量化步骤，也保存一个标准的PyTorch模型
    final_dense_path = get_save_path(f'{args.model_name}_final.pt', 'model')
    torch.save(model.state_dict(), final_dense_path)
    logger.info(f'最终稠密模型已保存到: {final_dense_path}')

    # 验证文件确实存在
    if os.path.exists(final_dense_path):
        file_size = os.path.getsize(final_dense_path) / 1024 / 1024
        logger.info(f'✅ 文件验证成功: {final_dense_path} ({file_size:.2f} MB)')
    else:
        logger.error(f'❌ 文件保存失败: {final_dense_path}')

    # 保存详细结果摘要
    summary_file = save_results_summary(original_accuracy, pruned_accuracy, final_accuracy, stats, args, args.model_name)
    
    # 总结
    logger.info("\n" + "="*60)
    logger.info(f"最终结果总结 - {args.model_name}:")
    logger.info(f"原始模型准确率: {original_accuracy:.4f}")
    logger.info(f"剪枝后准确率: {pruned_accuracy:.4f}")
    logger.info(f"微调后准确率: {final_accuracy:.4f}")
    logger.info(f"准确率下降: {(original_accuracy - final_accuracy):.4f} "
                f"({100 * (original_accuracy - final_accuracy) / original_accuracy:.2f}%)")
    logger.info(f"存储压缩比: {stats['compression_ratio']:.2f}x")
    logger.info(f"剪枝比例: {100 * stats['pruned_params'] / stats['total_params']:.2f}%")
    logger.info(f"原始大小: {stats['total_original_size'] / 1024 / 1024:.2f} MB")
    logger.info(f"压缩后大小: {stats['total_compressed_size'] / 1024 / 1024:.2f} MB")
    logger.info("="*60)
    logger.info("\n📁 保存的文件:")
    logger.info(f"   - 原始模型: {get_save_path(f'{args.model_name}_original.pt', 'model')}")
    logger.info(f"   - 剪枝后稀疏模型: {pruned_save_path}")
    logger.info(f"   - 最终稀疏模型: {final_save_path}")
    logger.info(f"   - 最终稠密模型: {final_dense_path}")
    logger.info(f"   - 详细日志: {get_save_path(f'{args.model_name}_bert_pruning.log', 'other')}")
    logger.info(f"   - 结果摘要: {summary_file}")
    logger.info("="*60)
    logger.info("\n🎯 技术要点:")
    logger.info("   - 实现了真正的结构化剪枝，而非仅使用mask")
    logger.info("   - 使用CSR稀疏矩阵格式存储剪枝后的权重")
    logger.info("   - 应用索引差压缩技术进一步减少存储空间")
    logger.info("   - 保持与后续量化和哈夫曼编码步骤的兼容性")
    logger.info("="*60)


def test_csr_implementation():
    """测试CSR稀疏矩阵实现的正确性"""
    print("测试CSR稀疏矩阵实现...")

    # 创建一个测试矩阵
    test_matrix = np.array([
        [1.0, 0.0, 2.0, 0.0],
        [0.0, 3.0, 0.0, 4.0],
        [5.0, 0.0, 0.0, 0.0],
        [0.0, 0.0, 6.0, 7.0]
    ], dtype=np.float32)

    print(f"原始矩阵:\n{test_matrix}")

    # 转换为CSR格式
    csr_matrix = dense_to_csr(test_matrix, 'fc')

    # 转换回稠密格式
    reconstructed = csr_matrix.to_dense()

    print(f"重建矩阵:\n{reconstructed}")

    # 检查是否相等
    if np.allclose(test_matrix, reconstructed):
        print("✅ CSR实现测试通过！")
    else:
        print("❌ CSR实现测试失败！")
        print(f"差异: {np.abs(test_matrix - reconstructed).max()}")

    # 显示压缩统计
    stats = csr_matrix.get_compression_stats()
    print(f"压缩统计: {stats}")


if __name__ == '__main__':
    # 如果传入了--test参数，运行测试
    import sys
    if '--test' in sys.argv:
        test_csr_implementation()
    else:
        main()