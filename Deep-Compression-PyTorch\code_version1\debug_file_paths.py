#!/usr/bin/env python3
"""
调试文件路径问题的脚本
"""

import os
import sys

def debug_file_paths():
    """调试文件路径问题"""
    print("🔍 调试文件路径问题")
    print("=" * 50)
    
    # 检查命令行参数
    print(f"命令行参数: {sys.argv}")
    
    # 模拟get_save_path函数
    def get_save_path(filename, file_type='other'):
        SAVE_DIRS = {
            'model': '/download/model',
            'image': '/download/image', 
            'other': '/download/other'
        }
        if file_type not in SAVE_DIRS:
            file_type = 'other'
        return os.path.join(SAVE_DIRS[file_type], filename)
    
    # 模拟get_model_path函数
    def get_model_path(model_name, step, extension='.pt'):
        return f"/download/model/{model_name}_{step}{extension}"
    
    # 测试不同的model_name值
    test_model_names = ['test_v3', 'bert_base_v1', 'experiment_v1']
    
    for model_name in test_model_names:
        print(f"\n📋 测试 model_name='{model_name}':")
        
        # bert_pruning.py 生成的路径
        pruning_paths = {
            'original': get_save_path(f'{model_name}_original.pt', 'model'),
            'final_dense': get_save_path(f'{model_name}_final.pt', 'model'),
            'pruned_sparse': get_save_path(f'{model_name}_pruned_sparse.pkl', 'model'),
            'final_sparse': get_save_path(f'{model_name}_final_sparse.pkl', 'model'),
        }
        
        # run_full_compression.py 期望的路径
        compression_paths = {
            'original': get_model_path(model_name, "original"),
            'final': get_model_path(model_name, "final"),
            'pruned': get_model_path(model_name, "pruned"),
        }
        
        print("  bert_pruning.py 生成的路径:")
        for key, path in pruning_paths.items():
            print(f"    {key}: {path}")
        
        print("  run_full_compression.py 期望的路径:")
        for key, path in compression_paths.items():
            print(f"    {key}: {path}")
        
        # 检查路径是否匹配
        final_match = pruning_paths['final_dense'] == compression_paths['final']
        print(f"  final路径匹配: {'✅' if final_match else '❌'}")
        if not final_match:
            print(f"    期望: {compression_paths['final']}")
            print(f"    实际: {pruning_paths['final_dense']}")
    
    print("\n" + "=" * 50)
    print("🔍 检查实际文件系统:")
    
    # 检查/download目录
    download_dir = "/download"
    if os.path.exists(download_dir):
        print(f"📁 {download_dir} 存在")
        
        model_dir = "/download/model"
        if os.path.exists(model_dir):
            print(f"📁 {model_dir} 存在")
            files = os.listdir(model_dir)
            print(f"📁 文件列表 ({len(files)} 个文件):")
            for f in sorted(files):
                full_path = os.path.join(model_dir, f)
                if os.path.isfile(full_path):
                    size = os.path.getsize(full_path) / 1024 / 1024
                    print(f"    {f} ({size:.2f} MB)")
        else:
            print(f"❌ {model_dir} 不存在")
    else:
        print(f"❌ {download_dir} 不存在")


if __name__ == '__main__':
    debug_file_paths()
