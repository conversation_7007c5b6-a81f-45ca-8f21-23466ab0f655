# modal_runner.py
import modal

# 创建镜像
# image = modal.Image.from_registry(
#     "pytorch/pytorch:2.3.0-cuda12.1-cudnn8-runtime"
# ).pip_install(
#     "matplotlib", 
#     "seaborn", 
#     "pillow", 
#     "torchmetrics[detection]",
#     "datasets", 
#     "timm", 
#     "scikit-learn", 
#     "tqdm",
#     "opencv-python-headless"  # 使用headless版本避免OpenGL依赖
# ).apt_install(
#     "libxml2-dev", 
#     "libxslt-dev",
#     "libglib2.0-0",           # OpenCV依赖
#     "libsm6",                 # OpenCV依赖
#     "libxext6",               # OpenCV依赖
#     "libxrender-dev",         # OpenCV依赖
#     "libgomp1"                # OpenMP支持
# ).add_local_dir("code_version_1", remote_path="/root/lab2")

image = modal.Image.from_registry(
    # "pytorch/pytorch:2.3.0-cuda12.1-cudnn8-runtime"
    "pytorch/pytorch:2.6.0-cuda12.4-cudnn9-runtime"
).pip_install(
    # 你已有的包
    "matplotlib",
    "seaborn",
    "pillow",
    "torchmetrics[detection]",
    "datasets>=2.0.0",        # 显式版本下限
    "timm",
    "scikit-learn>=1.0.0",
    "tqdm>=4.64.0",
    "opencv-python-headless",

    # 为新代码补充／升级的包
    "transformers>=4.20.0",
    "accelerate>=0.20.0",
    "numpy>=1.21.0"
).apt_install(
    # apt 部分保持不变
    "libxml2-dev",
    "libxslt-dev",
    "libglib2.0-0",
    "libsm6",
    "libxext6",
    "libxrender-dev",
    "libgomp1"
).add_local_dir("code_version1", remote_path="/root/lab2")




# 创建持久化卷
# volume = modal.Volume.from_name("lab2-faster_rcnn", create_if_missing=True)
volume = modal.Volume.from_name("bert_compress", create_if_missing=True)

# image = modal.Image.debian_slim().add_local_dir("./test_modal", remote_path="/root/test_modal")
app = modal.App("bert_compress", image=image)


@app.function(
    timeout=18000,
    gpu="A100-40GB",
    volumes={"/download": volume} 
)
def run_project():
    import sys
    import os
    sys.path.append('/root/lab2')
    # from hello import main
    # main()
    from run_full_compression import run_compression
    
    # 🔥 使用新的包装函数，更方便传递参数
    # 示例1: 完整压缩流程 (剪枝+量化+哈夫曼编码)
    # 🔥 优化后的配置：更激进的剪枝以获得更好的压缩比
    success = run_compression(
        model_name='test_v6',
        steps='all',
        prune_method='percentile',  # 使用百分位数剪枝，通常更有效
        percentile=50.0,           # 剪枝最小的15%权重，更激进
        epochs=2,                  # 增加微调轮数以恢复精度
        max_samples=10,            # 增加样本数，避免过拟合
        num_bits=8                 # 量化位数
    )

    # 💡 如果上面的配置压缩效果仍不理想，可以尝试更激进的配置：
    # success = run_compression(
    #     model_name='test_v5_aggressive',
    #     steps='all',
    #     prune_method='std',
    #     sensitivity=0.5,           # 更激进的标准差剪枝
    #     epochs=4,                  # 更多微调轮数
    #     max_samples=100,           # 更多样本
    #     num_bits=8
    # )
    
    # 💡 其他使用示例 (注释掉，需要时可以启用):
    
    # 示例2: 仅剪枝
    # success = run_compression(
    #     model_name='modal_prune_only',
    #     steps='pruning',
    #     prune_method='std',
    #     sensitivity=0.3,
    #     epochs=3,
    #     max_samples=3000
    # )
    
    # 示例3: 仅量化
    # success = run_compression(
    #     model_name='modal_quant_only',
    #     steps='quantization',
    #     num_bits=6
    # )
    
    # 示例4: 剪枝+量化
    # success = run_compression(
    #     model_name='modal_prune_quant',
    #     steps='pruning+quantization',
    #     prune_method='percentile',
    #     percentile=5.0,
    #     num_bits=4,
    #     epochs=3,
    #     max_samples=8000
    # )
    
    # 示例5: 高压缩比实验
    # success = run_compression(
    #     model_name='modal_high_compression',
    #     steps='all',
    #     sensitivity=0.4,   # 更激进的剪枝
    #     num_bits=4,        # 更低的位宽
    #     epochs=4,
    #     batch_size=8
    # )
    
    if success:
        print("🎉 压缩管道执行成功!")
    else:
        print("❌ 压缩管道执行失败!")
    # from run_huffman_encode import run_huffman_encode
    # run_huffman_encode()
    # from run_quantization import run_quantization
    # run_quantization()
    # from run_bert_pruning_modal import main
    # main(['--percentile_pruning', '--percentile', '75.0'])
    # main(['--std_pruning', '--sensitivity', '0.25'])
    # 6. 提交更改到卷（重要！）
    volume.commit()
    print("✅ 已提交更改到持久化卷")
    



@app.local_entrypoint()
def local_main():
    run_project.remote()